from PySide6.QtCore import Qt, Signal, QCoreApplication
from PySide6.QtGui import QGuiApplication, QIcon
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QStackedWidget, QTabWidget, Q<PERSON><PERSON><PERSON>Layout, Q<PERSON><PERSON>Button

from src.common.controller.controller_manager import controller_manager, Controller
from src.common.controller.main_controller import main_controller, connect_slot
from src.common.model.user_model import user_model_manager
from src.common.model.user_role_model import role_model_manager
from src.common.widget.custom_titlebar.custom_titlebar_with_tab import CustomTitleBarWithTab
from src.common.widget.dialogs.dialogs_permission_screen import AddUserDialog, AddRoleDialog
from src.common.widget.search_widget.search_bar import SearchBar
from src.presentation.user_permissions_screen.widgets.user_group_tableview import UserGroupsTableView
from src.presentation.user_permissions_screen.widgets.users_tableview import UsersTableView
from src.styles.style import Style

import logging

from src.utils.theme_setting import theme_setting
logger = logging.getLogger(__name__)

class FilterUserType:
    FilterUser = 'FILTER_USER'

class FilterRoleType:
    FilterRole = 'FILTER_ROLE'

class UserPermissionsScreen(QWidget):

    def __init__(self, parent=None, window_parent=None):
        super(UserPermissionsScreen, self).__init__(parent)
        self.icon_camera_group = None
        self.new_add_camera_dialog = None
        self.parent = parent
        self.window_parent = window_parent

        main_controller.list_parent['UserPermissionsScreen'] = self

        self.health_check_camera_thread = None

        self.calculate_layout()

        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.main_layout.setContentsMargins(4, 0, 2, 4)
        self.main_layout.setSpacing(0)

        self.title_bar = CustomTitleBarWithTab(parent=self, window_parent=self.window_parent, is_show_tab_bar=True)
        self.title_bar.setFixedHeight(40)
        self.title_bar.signal_change_tab.connect(self.on_tab_change)
        self.main_layout.addWidget(self.title_bar)

        self.layout_content = QVBoxLayout()
        self.layout_content.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_content.setContentsMargins(0, 0, 0, 0)
        self.stacked_content = QStackedWidget()
        self.stacked_content.setContentsMargins(0, 0, 0, 0)
        self.stacked_content.setObjectName("stacked_content")
        self.layout_content.addWidget(self.stacked_content)
        self.main_layout.addLayout(self.layout_content)
        self.setLayout(self.main_layout)

        self.setObjectName("device_screen_widget")

        self.image_list = {}
        self.connect_slot()
        self.restyle_user_screen()

    def connect_slot(self):
        connect_slot(
            (main_controller.complete_fetching_data, self.complete_fetching_data),
            (controller_manager.exit_controller_signal, self.exit_controller_signal)
        )

    def complete_fetching_data(self, data):
        controller: Controller = data
        main_controller.current_controller = controller
        # add Widget truoc khi add tab vao tab bar de dam bao widget da duoc add thanh cong
        # khi do moi co widget de co the setCurrentIndex
        tab_user_permissions = UserPermissionsWidget(controller=controller)
        self.stacked_content.addWidget(tab_user_permissions)
        self.title_bar.add_Tab(controller.server.data.server_ip)
        self.title_bar.tab_widget.tabBar().show()
        self.stacked_content.setCurrentIndex(self.title_bar.tab_widget.tabBar().currentIndex())

        list_users = user_model_manager.get_user_list(server_ip=controller.server.data.server_ip)
        # print(f"HanhLT: list_users = {list_users}")
        # list_groups = group_model_manager.get_group_list(server_ip=controller.server.data.server_ip)
        tab_user_permissions.update_data_to_table(user_datas=list_users, user_group_datas=None)

    def exit_controller_signal(self, data):
        controller: Controller = data
        for idx in range(self.title_bar.tab_widget.count()):
            if self.title_bar.tab_widget.tabText(idx) == controller.server.data.server_ip:
                widget = self.stacked_content.widget(idx)
                self.stacked_content.removeWidget(widget)
                widget.deleteLater()
                self.title_bar.tab_widget.tabBar().tabCloseRequested.emit(idx)
                break
        self.title_bar.tab_widget.tabBar().show()

    def on_tab_change(self, index):
        # logger.debug(f'on_tab_change = {index}')
        # Show the corresponding content widget when a tab is selected
        self.stacked_content.setCurrentIndex(index)
        tab_device_widget = self.stacked_content.currentWidget()
        if tab_device_widget is not None:
            main_controller.current_controller = tab_device_widget.controller

    def calculate_layout(self, desktop_screen_size=None):
        if desktop_screen_size is None:
            screen = QGuiApplication.primaryScreen()
            desktop_screen_size = screen.availableGeometry()
        screen_available_width = desktop_screen_size.width()
        screen_available_height = desktop_screen_size.height()
        self.control_width = 0.97 * screen_available_width
        self.control_height = 0.08 * screen_available_height
        self.table_height = 0.85 * screen_available_height
        self.table_width = 0.96 * screen_available_width

    def re_translate_user_screen(self):
        for i in range(self.stacked_content.count()):
            widget = self.stacked_content.widget(i)
            if isinstance(widget, UserPermissionsWidget):
                widget.retranslate_ui_tab_user()
    
    def restyle_user_screen(self):
        self.title_bar.set_dynamic_stylesheet()
        if self.stacked_content.currentWidget() is not None:
            self.stacked_content.currentWidget().set_dynamic_stylesheet()
        self.stacked_content.setStyleSheet(f'''
            QWidget#stacked_content {{
                border-top: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
            }}
            ''')
        
        # self.setStyleSheet(
        #     f'''
        #         QWidget#device_screen_widget {{
        #             background-color: {Style.PrimaryColor.on_background};
        #             color: Style.PrimaryColor.white;
        #         }}
        #         QLabel {{
        #             color: {Style.PrimaryColor.white_2};
        #         }}
        #         QStandardItem {{
        #             color: #000000;
        #         }}
        #         '''
        # )
        
        # self.setStyleSheet(
        #     f'''
        #     QWidget#device_screen_widget {{
        #         background-color: {Style.PrimaryColor.test};
        #         color: Style.PrimaryColor.white;
        #     }}
        #     QLabel {{
        #         color: {Style.PrimaryColor.white_2};
        #     }}
        #     QStandardItem {{
        #         color: #000000;
        #     }}
        #     '''
        # )


class UserPermissionsWidget(QWidget):
    def __init__(self, parent=None, controller: Controller = None):
        super().__init__(parent)
        self.control_height = None
        self.table_height = None
        self.table_width = None
        self.control_width = None
        self.controller = controller
        self.calculate_layout()
        self.connect_slot()
        self.load_ui()
        self.set_dynamic_stylesheet()

    def calculate_layout(self, desktop_screen_size=None):
        current_screen = self.screen()
        if current_screen:
            screen_geometry = current_screen.geometry()
            screen_width = screen_geometry.width()
            screen_height = screen_geometry.height()
            self.control_width = 0.97 * screen_width
            self.control_height = 0.08 * screen_height
            self.table_height = 0.85 * screen_height
            self.table_width = 0.96 * screen_width

    def load_ui(self):
        self.widget_users_management = QWidget()
        self.widget_users_management.setObjectName("widget_users_management")
        # self.widget_users_management.setStyleSheet(f"""
        #     QWidget#widget_users_management {{
        #         background-color: {Style.PrimaryColor.on_background};
        #     }}
        #     """)
        self.layout_users_management = QVBoxLayout(self.widget_users_management)
        self.layout_users_management.setContentsMargins(0, 0, 0, 0)
        self.layout_users_management.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.setup_ui_users_management()

        self.widget_user_groups_management = QWidget()
        self.widget_user_groups_management.setObjectName("widget_user_groups_management")
        # self.widget_user_groups_management.setStyleSheet(f"""
        #             QWidget#widget_user_groups_management {{
        #                 background-color: {Style.PrimaryColor.on_background};
        #             }}
        #             """)
        self.layout_user_groups_management = QVBoxLayout(self.widget_user_groups_management)
        self.layout_user_groups_management.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_user_groups_management.setContentsMargins(0, 0, 0, 0)
        self.setup_ui_user_groups_management()

        self.tab_content_widget = QTabWidget()
        self.tab_content_widget.addTab(self.widget_users_management, self.tr('Users Management'))
        self.tab_content_widget.addTab(self.widget_user_groups_management, self.tr('User Groups Management'))

        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        self.main_layout.addWidget(self.tab_content_widget)
        self.setLayout(self.main_layout)

    def connect_slot(self):
        connect_slot(
            (user_model_manager.add_user_signal, self.add_user_slot),
            (user_model_manager.delete_user_model_signal, self.delete_user),
            (role_model_manager.add_role_signal, self.add_role_slot),
            (role_model_manager.delete_role_model_signal, self.delete_single_role),
        )

    def setup_ui_users_management(self):
        self.widget_top_users_management = QWidget()
        self.layout_top_users_management = QHBoxLayout(self.widget_top_users_management)

        self.search_users_widget = SearchBar(parent=self, title=self.tr("Search"))
        self.search_users_widget.search_bar.setPlaceholderText(self.tr("Search by name, email, phone number, group"))
        self.search_users_widget.search_bar.setFixedHeight(40)
        # self.search_users_widget.label_title.setStyleSheet(f"color: {Style.PrimaryColor.white_2};")
        self.search_users_widget.search_items_signal.connect(self.search_users_name)
        layout_search = QHBoxLayout()
        layout_search.setAlignment(Qt.AlignmentFlag.AlignLeft)
        layout_search.addWidget(self.search_users_widget)

        self.layout_button_users = QHBoxLayout()
        self.layout_button_users.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignBottom)
        # self.btn_refresh_users = QPushButton(self.tr("Refresh"))
        # self.btn_refresh_users.setIcon(QIcon(Style.PrimaryImage.refresh))
        # self.btn_refresh_users.setStyleSheet(Style.StyleSheet.button_negative)
        # self.btn_refresh_users.setFixedHeight(40)
        # self.btn_refresh_users.clicked.connect(self.handle_refresh_users_click)

        # self.btn_delete_users = QPushButton(self.tr("Delete"))
        # self.btn_delete_users.setIcon(QIcon(Style.PrimaryImage.delete_icon))
        # self.btn_delete_users.setStyleSheet(Style.StyleSheet.button_negative)
        # self.btn_delete_users.setFixedHeight(40)
        # self.btn_delete_users.clicked.connect(self.handle_delete_users)

        self.btn_add_users = QPushButton(self.tr("Add User"))
        # self.btn_add_users.setIcon(QIcon(Style.PrimaryImage.add))
        # self.btn_add_users.setStyleSheet(Style.StyleSheet.style_button_add)
        self.btn_add_users.setFixedHeight(40)
        self.btn_add_users.clicked.connect(self.handle_add_users)

        # self.layout_button_users.addWidget(self.btn_refresh_users)
        self.layout_button_users.addWidget(self.btn_add_users)
        # self.layout_button_users.addWidget(self.btn_delete_users)

        self.layout_top_users_management.addLayout(layout_search)
        self.layout_top_users_management.addLayout(self.layout_button_users)
        self.layout_top_users_management.setStretch(0, 20)
        self.layout_top_users_management.setStretch(1, 80)

        # Table
        self.table_users_management = UsersTableView(widget_width=self.table_width, widget_height=self.table_height, controller=self.controller)

        self.layout_users_management.addWidget(self.widget_top_users_management)
        self.layout_users_management.addWidget(self.table_users_management)

    def setup_ui_user_groups_management(self):
        self.widget_top_user_groups_management = QWidget()
        self.layout_top_user_groups_management = QHBoxLayout(self.widget_top_user_groups_management)

        self.search_user_groups_widget = SearchBar(parent=self, title=self.tr("Search"))
        self.search_user_groups_widget.search_bar.setPlaceholderText(self.tr("Search by name"))
        self.search_user_groups_widget.search_bar.setFixedHeight(40)
        self.search_user_groups_widget.label_title.setStyleSheet(f"color: {Style.PrimaryColor.white_2};")
        self.search_user_groups_widget.search_items_signal.connect(self.search_user_groups_name)
        layout_search = QHBoxLayout()
        layout_search.setAlignment(Qt.AlignmentFlag.AlignLeft)
        layout_search.addWidget(self.search_user_groups_widget)

        self.layout_button_user_groups = QHBoxLayout()
        self.layout_button_user_groups.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignBottom)
        # self.btn_refresh_user_groups = QPushButton(self.tr("Refresh"))
        # self.btn_refresh_user_groups.setFixedHeight(40)
        # self.btn_refresh_user_groups.clicked.connect(self.handle_refresh_user_groups_click)

        # self.btn_delete_user_groups = QPushButton(self.tr("Delete"))
        # self.btn_delete_user_groups.setFixedHeight(40)
        # self.btn_delete_user_groups.clicked.connect(self.handle_delete_user_groups)

        self.btn_add_user_groups = QPushButton(self.tr("Add User Group"))
        self.btn_add_user_groups.setFixedHeight(40)
        self.btn_add_user_groups.clicked.connect(self.handle_add_user_groups)

        # self.layout_button_user_groups.addWidget(self.btn_refresh_user_groups)
        self.layout_button_user_groups.addWidget(self.btn_add_user_groups)
        # self.layout_button_user_groups.addWidget(self.btn_delete_user_groups)

        self.layout_top_user_groups_management.addLayout(layout_search)
        self.layout_top_user_groups_management.addLayout(self.layout_button_user_groups)
        self.layout_top_user_groups_management.setStretch(0, 20)
        self.layout_top_user_groups_management.setStretch(1, 80)

        # Table
        self.table_user_groups_management = UserGroupsTableView(widget_width=self.table_width, widget_height=self.table_height, controller=self.controller)

        self.layout_user_groups_management.addWidget(self.widget_top_user_groups_management)
        self.layout_user_groups_management.addWidget(self.table_user_groups_management)


    # Search slot
    def search_users_name(self, text):
        if text != '':
            self.table_users_management.filter_user_name = True
            self.table_users_management.put_filter_queue(text)
        else:

            self.table_users_management.filter_user_name = False
            self.table_users_management.put_filter_queue(text)

    def search_user_groups_name(self, text):
        if text != '':
            self.table_user_groups_management.filter_role_name = True
            self.table_user_groups_management.put_filter_queue(text)
        else:

            self.table_user_groups_management.filter_role_name = False
            self.table_user_groups_management.put_filter_queue(text)

    # # Button click slot
    # def handle_refresh_users_click(self):
    #     self.table_users_management.refresh_table()

    # def handle_refresh_user_groups_click(self):
    #     self.table_user_groups_management.refresh_table()

    # def handle_delete_users(self):
    #     print('Enable click')

    # def handle_delete_user_groups(self):
    #     pass

    def handle_add_users(self):
        dialog_add_users = AddUserDialog(parent=self, controller=self.controller)
        dialog_add_users.exec()

    def handle_add_user_groups(self):
        dialog_add_roles = AddRoleDialog(parent=self, controller=self.controller)
        dialog_add_roles.exec()

    # Update data
    def update_data_to_table(self, user_datas=None, user_group_datas=None):

        # add camera data
        self.table_users_management.create_user_data(is_thread=False)
        self.table_users_management.create_model()

        # add group data
        self.table_user_groups_management.create_roles_data(is_thread=False)
        self.table_user_groups_management.create_model()
       

    def add_user_slot(self, user):
        # Cap nhat get_camera_parameters
        user_list = user_model_manager.get_user_list(self.controller.server.data.server_ip)
        self.table_users_management.put_filter_queue(self.search_users_widget.search_bar.text())

    def add_role_slot(self):
        self.table_user_groups_management.put_filter_queue(self.search_user_groups_widget.search_bar.text())

    def delete_user(self, data):
        self.table_users_management.put_filter_queue(self.search_users_widget.search_bar.text())

    def delete_single_role(self, data):
        self.table_user_groups_management.put_filter_queue(self.search_user_groups_widget.search_bar.text())

    # Localization
    def retranslate_ui_tab_user(self):
        # Tab
        self.tab_content_widget.setTabText(0, QCoreApplication.translate("UserPermissionsWidget", u"Users Management", None))
        self.tab_content_widget.setTabText(1, QCoreApplication.translate("UserPermissionsWidget", u"User Groups Management", None))

        # User widget
        self.search_users_widget.label_title.setText(QCoreApplication.translate("UserPermissionsWidget", u"Search", None))
        self.search_users_widget.search_bar.setPlaceholderText(QCoreApplication.translate("UserPermissionsWidget", u'Search by name, email, phone number', None))
        # self.btn_refresh_users.setText(QCoreApplication.translate("UserPermissionsWidget", u'Refresh', None))
        # self.btn_delete_users.setText(QCoreApplication.translate("UserPermissionsWidget", u'Delete', None))
        self.btn_add_users.setText(QCoreApplication.translate("UserPermissionsWidget", u'Add User', None))
        self.table_users_management.retranslate_table_user()

        # User role widget
        self.search_user_groups_widget.label_title.setText(QCoreApplication.translate("UserPermissionsWidget", u"Search", None))
        self.search_user_groups_widget.search_bar.setPlaceholderText(QCoreApplication.translate("UserPermissionsWidget", u'Search by name', None))
        # self.btn_refresh_user_groups.setText(QCoreApplication.translate("UserPermissionsWidget", u'Refresh', None))
        # self.btn_delete_user_groups.setText(QCoreApplication.translate("UserPermissionsWidget", u'Delete', None))
        self.btn_add_user_groups.setText(QCoreApplication.translate("UserPermissionsWidget", u'Add User Group', None))
        self.table_user_groups_management.retranslate_table_role()

        self.update()

    def set_dynamic_stylesheet(self):
        self.table_users_management.set_dynamic_stylesheet()
        self.table_user_groups_management.set_dynamic_stylesheet()
        self.tab_content_widget.setStyleSheet(
            f"""
                QTabWidget::pane {{ /* The tab widget frame */
                    background-color: transparent;
                }}
                QTabBar::pane {{
                    background: transparent;
                    border: None;
                    font-weight: None
                }}
                QTabBar::tab {{
                    background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                    color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                    padding: 8px 8px;
                    font-weight: None;
                    border: None;
                    height: 16px;
                }}
                QTabBar::tab:selected {{
                    padding: 8px 8px;
                    font-weight: bold;
                    border-bottom: 2px solid {main_controller.get_theme_attribute("Color", "primary")};
                }}
                QTabBar::scroller {{width: 0px;}}
                QTabBar QToolButton {{ /* the scroll buttons are tool buttons */
                    width: 0;
                    border-width: 0px;
                }}
                QTabBar::tear {{
                    width: 0px; 
                    border: none;
                }}
                """
        )

        self.widget_users_management.setStyleSheet(f"""
            QWidget#widget_users_management {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
            }}
            """)
        self.widget_user_groups_management.setStyleSheet(f"""
            QWidget#widget_user_groups_management {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
            }}
            """)

        self.search_users_widget.set_dynamic_stylesheet()
        # self.btn_refresh_users.setStyleSheet(f'''
        #     QPushButton {{
        #         background-color: {Style.PrimaryColor.background_refresh_button};
        #         color: white;
        #         border-radius: 4px;
        #         padding: 4px 16px 4px 16px;
        #         qproperty-icon: url({Style.PrimaryImage.refresh});
        #     }}
        #     QPushButton:hover {{
        #             background-color: {Style.PrimaryColor.hover_refresh_button};

        #         }}
        #     QPushButton:pressed {{
        #             background-color: {Style.PrimaryColor.hover_refresh_button};

        #         }}
        #     QPushButton:disabled {{
        #             background-color: {main_controller.get_theme_attribute("Color", "table_normal_button_background")};

        #         }}
        # ''')

        # self.btn_delete_users.setStyleSheet(f'''
        #     QPushButton {{
        #         background-color: {Style.PrimaryColor.background_delete_button};
        #         color: white;
        #         border-radius: 4px;
        #         padding: 4px 16px 4px 16px;
        #         qproperty-icon: url({Style.PrimaryImage.delete_icon});
        #     }}
        #     QPushButton:hover {{
        #             background-color: {Style.PrimaryColor.hover_delete_button};

        #         }}
        #     QPushButton:pressed {{
        #             background-color: {Style.PrimaryColor.hover_delete_button};

        #         }}
        #     QPushButton:disabled {{
        #             background-color: {main_controller.get_theme_attribute("Color", "table_normal_button_background")};

        #         }}
        # ''')
        self.btn_add_users.setIcon(QIcon(main_controller.get_theme_attribute("Image", "add_button_icon")))
        self.btn_add_users.setStyleSheet(Style.PrimaryStyleSheet.get_add_button_style(theme_instance=main_controller))

        self.search_user_groups_widget.set_dynamic_stylesheet()
        # self.btn_refresh_user_groups.setStyleSheet(f'''
        #     QPushButton {{
        #         background-color: {Style.PrimaryColor.background_refresh_button};
        #         color: white;
        #         border-radius: 4px;
        #         padding: 4px 16px 4px 16px;
        #         qproperty-icon: url({Style.PrimaryImage.refresh});
        #     }}
        #     QPushButton:hover {{
        #             background-color: {Style.PrimaryColor.hover_refresh_button};

        #         }}
        #     QPushButton:pressed {{
        #             background-color: {Style.PrimaryColor.hover_refresh_button};

        #         }}
        #     QPushButton:disabled {{
        #             background-color: {main_controller.get_theme_attribute("Color", "table_normal_button_background")};

        #         }}
        # ''')

        # self.btn_delete_user_groups.setStyleSheet(f'''
        #     QPushButton {{
        #         background-color: {Style.PrimaryColor.background_delete_button};
        #         color: white;
        #         border-radius: 4px;
        #         padding: 4px 16px 4px 16px;
        #         qproperty-icon: url({Style.PrimaryImage.delete_icon});
        #     }}
        #     QPushButton:hover {{
        #             background-color: {Style.PrimaryColor.hover_delete_button};

        #         }}
        #     QPushButton:pressed {{
        #             background-color: {Style.PrimaryColor.hover_delete_button};

        #         }}
        #     QPushButton:disabled {{
        #             background-color: {main_controller.get_theme_attribute("Color", "table_normal_button_background")};
        #         }}
        # ''')
        self.btn_add_user_groups.setIcon(QIcon(main_controller.get_theme_attribute("Image", "add_button_icon")))
        self.btn_add_user_groups.setStyleSheet(Style.PrimaryStyleSheet.get_add_button_style(theme_instance=main_controller))

    def resizeEvent(self, event):
        self.calculate_layout()
        self.table_users_management.resize_ui_table_user(self.table_width, self.table_height)
        self.table_user_groups_management.resize_ui_table_groups(self.table_width, self.table_height)
        super().resizeEvent(event)
