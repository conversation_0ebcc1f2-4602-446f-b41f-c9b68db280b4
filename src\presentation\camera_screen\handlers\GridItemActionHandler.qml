import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import models 1.0
import "../constants/ZIndexConstants.js" as ZIndex


Item {
    id: root
    anchors.fill: parent
    focus: true

    property var gridItem: null
    property var contextMenuLoader: null
    property bool isDarkTheme: gridItem ? gridItem.isDarkTheme : true
    property bool isSelected: gridItem ? gridItem.isSelected : false
    property bool isMaximized: root.isMaximized || (root.itemData && root.itemData.fullscreen)
    property bool isVirtualGrid: gridItem ? gridItem.isVirtualGrid : false
    property bool isHovered: false

    property string defaultBorderColor: "transparent"
    property string hoverBorderColor: isDarkTheme ? "#4fd1c5" : "#4fd1c5"
    property string selectedBorderColor: isDarkTheme ? "#4fd1c5" : "#4fd1c5"

    property bool isDragging: false
    property bool isClick: false
    property real dragThreshold: 10
    property real pressX: 0
    property real pressY: 0
    property real startX: 0
    property real startY: 0
    property real startWidth: 0
    property real startHeight: 0
    property bool isSwapping: false
    property bool dragActive: false
    property int lastDragTime: 0
    property int dragCooldown: 200
    property var dragDimensions: gridItem && gridItem.gridModel ? gridItem.gridModel.getCellDimensions(gridItem.gridRow, gridItem.gridCol) : {"width": 1, "height": 1}

    property bool isResizing: false
    property string resizeType: ""
    property real initialWidth: 0
    property real initialHeight: 0
    property var resizeState: null

    property bool isGridLocked: gridItem && gridItem.gridModel ? (gridItem.gridModel.isAnimating || false) : false

    signal hoverChanged(bool isHovered)
    signal itemClicked(var item)
    signal itemDoubleClicked(var item)
    signal gridScaleRequested(int delta)



    MouseArea {
        id: dragArea
        anchors.fill: parent
        hoverEnabled: true
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        drag.target: !isMaximized && gridItem ? gridItem : undefined
        drag.axis: Drag.XAndYAxis
        drag.smoothed: true
        drag.filterChildren: true

        propagateComposedEvents: true
        // enabled: !(gridItem && gridItem.itemData && gridItem.itemData.isAnimating)

        onEntered: {
            isHovered = true
            if (gridItem) {
                gridItem.isHovered = true
                root.hoverChanged(true)
            }
        }

        onExited: {
            isHovered = false
            if (gridItem) {
                gridItem.isHovered = false
                root.hoverChanged(false)
            }
        }

        property bool dragActive: false
        property bool dragPrepared: false
        property real pressX: 0
        property real pressY: 0

        onPressed: function(mouse) {
            if (isGridLocked) { mouse.accepted = false; return; }
            if (gridItem && gridItem.itemData && gridItem.itemData.isAnimating) {
                mouse.accepted = false;
                return;
            }
            if (mouse.button === Qt.LeftButton) {
                // ✅ PTZ STATE: Deactivate PTZ for all cameras when clicking on any camera
                if (gridItem && gridItem.gridModel && gridItem.gridModel.activatePtzForCamera) {
                    var position = gridItem.gridRow * gridItem.gridModel.columns + gridItem.gridCol
                    gridItem.gridModel.activatePtzForCamera(position, "none")
                    console.log("🎮 [PTZ_DEACTIVATE] Camera clicked - deactivating all PTZ modes")
                }

                var shiftPressed = mouse.modifiers & Qt.ShiftModifier

                if (shiftPressed) {
                    dragActive = false
                    dragPrepared = false
                    drag.target = undefined

                    var mainGrid = findMainGrid()
                    if (mainGrid && mainGrid.gridActionHandler) {
                        var globalPos = gridItem.mapToItem(mainGrid, mouse.x, mouse.y)
                        mainGrid.gridActionHandler.handleShiftPress(globalPos.x, globalPos.y, mouse.modifiers)
                    }

                    mouse.accepted = true
                } else {
                    dragActive = false
                    dragPrepared = true
                    pressX = mouse.x
                    pressY = mouse.y
                    drag.target = (!isMaximized && !root.dragDisabled && !root.blockDragUntilRelease && !(gridItem && gridItem.itemData && gridItem.itemData.isAnimating) && gridItem) ? gridItem : undefined
                }
            }
        }

        onPositionChanged: function(mouse) {
            if (isGridLocked) { mouse.accepted = false; return; }
            var shiftPressed = mouse.modifiers & Qt.ShiftModifier
            if (shiftPressed && pressed) {
                var mainGrid = findMainGrid()
                if (mainGrid && mainGrid.gridActionHandler) {
                    var globalPos = gridItem.mapToItem(mainGrid, mouse.x, mouse.y)
                    mainGrid.gridActionHandler.handleShiftMove(globalPos.x, globalPos.y, mouse.modifiers)
                }
                mouse.accepted = true
                return
            }

            if (pressed && dragPrepared) {
                var deltaX = Math.abs(mouse.x - pressX)
                var deltaY = Math.abs(mouse.y - pressY)
                var dragThreshold = 5

                if (!dragActive && (deltaX > dragThreshold || deltaY > dragThreshold)) {
                    dragActive = true

                    var mainGrid = findMainGrid()
                    if (mainGrid && mainGrid.startItemDrag) {
                        mainGrid.startItemDrag(gridItem, mouse.x, mouse.y)
                    }

                    if (gridItem && !gridItem.isMaximized) {
                        gridItem.z = ZIndex.contentDragged
                        gridItem.isDragging = true
                        gridItem.dragStarted(gridItem)
                        
                        // Use centralized z-index management
                        gridItem.updateParentZIndex()
                    }
                }

                if (dragActive) {
                    var mainGrid = findMainGrid()
                    if (mainGrid && mainGrid.updateItemDrag) {
                        mainGrid.updateItemDrag(gridItem, mouse.x, mouse.y)
                    }
                }
            }
        }

        onReleased: function(mouse) {
            if (isGridLocked) { mouse.accepted = false; return; }
            // Chỉ chặn drag khi fullscreen với chuột trái
            if (mouse.button === Qt.LeftButton && (isMaximized || (gridItem.itemData && gridItem.itemData.fullscreen))) {
                mouse.accepted = false;
                dragActive = false;
                dragPrepared = false;
                drag.target = undefined;
                return;
            }
            var shiftPressed = mouse.modifiers & Qt.ShiftModifier
            if (shiftPressed) {
                var mainGrid = findMainGrid()
                if (mainGrid && mainGrid.gridActionHandler) {
                    var globalPos = gridItem.mapToItem(mainGrid, mouse.x, mouse.y)
                    mainGrid.gridActionHandler.handleShiftRelease(globalPos.x, globalPos.y, mouse.modifiers)
                }
                dragActive = false
                dragPrepared = false
                drag.target = !isMaximized && gridItem ? gridItem : undefined
                mouse.accepted = true
                return
            }

            // Cho phép context menu chuột phải kể cả khi fullscreen
            if (mouse.button === Qt.RightButton && !dragActive && contextMenuLoader) {
                contextMenuLoader.active = true
                var pos = gridItem.mapToItem(gridItem.Window.window.contentItem, mouse.x, mouse.y)
                contextMenuLoader.item.x = pos.x
                contextMenuLoader.item.y = pos.y
                contextMenuLoader.item.open()
            }

            if (dragActive) {
                var mainGrid = findMainGrid()
                if (mainGrid && mainGrid.endItemDrag) {
                    var targetRowCol = mainGrid.endItemDrag(gridItem, mouse.x, mouse.y)
                    gridItem.snapToGridRowCol(gridItem.gridRow, gridItem.gridCol)
                }

                if (gridItem) {
                    gridItem.dragEnded(gridItem)
                    gridItem.dragCompleted()
                }
            } else if (dragPrepared && mouse.button === Qt.LeftButton) {
                var ctrlPressed = mouse.modifiers & Qt.ControlModifier
                var shiftPressed = mouse.modifiers & Qt.ShiftModifier

                // ✅ PTZ STATE: Deactivate PTZ for other cameras when clicking on this grid item
                if (gridItem && gridItem.gridModel && gridItem.gridModel.activatePtzForCamera) {
                    // Get current position
                    var position = gridItem.gridRow * gridItem.gridModel.columns + gridItem.gridCol
                    // Deactivate PTZ for all cameras (including this one) when clicking on grid item
                    gridItem.gridModel.activatePtzForCamera(position, "none")
                }
                if (!ctrlPressed && !shiftPressed) {
                    // trigger để lấy dữ liệu videoplayback
                    if (gridItem && gridItem.handleItemClicked) {
                        gridItem.handleItemClicked()
                    }
                    
                }
                if (gridItem && gridItem.handleItemSelection) {
                    gridItem.handleItemSelection(gridItem, ctrlPressed, shiftPressed)
                } else {
                    itemClicked(gridItem)
                }
            }

            dragActive = false
            dragPrepared = false
            if (gridItem) {
                gridItem.isDragging = false
                gridItem.z = ZIndex.contentNormal
                
                // Use centralized z-index management
                gridItem.updateParentZIndex()
            }
        }

        onCanceled: {
            if (isGridLocked) { mouse.accepted = false; return; }
            if (dragActive) {
                var mainGrid = findMainGrid()
                if (mainGrid && mainGrid.endItemDrag) {
                    mainGrid.endItemDrag(gridItem, 0, 0)
                }
            }

            dragActive = false
            dragPrepared = false
            if (gridItem) {
                gridItem.isDragging = false
                gridItem.z = ZIndex.contentNormal
                gridItem.snapToGridRowCol(gridItem.gridRow, gridItem.gridCol)
                
                // Use centralized z-index management
                gridItem.updateParentZIndex()
            }
        }

        onDoubleClicked: function(mouse) {
            if (isGridLocked) { mouse.accepted = false; return; }
            if (mouse.button === Qt.LeftButton) {
                // ✅ CENTRALIZED: Use reusable fullscreen handler
                if (gridItem && gridItem.itemData) {
                    var targetState = !gridItem.itemData.fullscreen

                    if (gridItem.animationManager) {
                        var success = gridItem.animationManager.handleFullscreenTransition(
                            gridItem, targetState, "ACTION_HANDLER"
                        )

                        if (success) {
                            itemDoubleClicked(gridItem)
                        }
                    } else {
                        console.warn("[ACTION_HANDLER] AnimationManager reference not found")
                    }
                }
            }
        }

        onWheel: function(wheel) {
            if (isGridLocked) { wheel.accepted = false; return; }
            if (wheel.modifiers & Qt.ControlModifier) {
                var mainGrid = findMainGrid()
                if (mainGrid && mainGrid.gridLinesOverlay) {
                    mainGrid.gridLinesOverlay.startCtrlWheelMode()
                }

                // Gọi trực tiếp gridModel.handleCtrlWheel thay vì emit signal
                if (gridItem && gridItem.gridModel) {
                    gridItem.gridModel.handleCtrlWheel(wheel.angleDelta.y)
                }

                wheel.accepted = true
                return
            }

            if (gridItem && gridItem.itemData && gridItem.itemData.cameraModel) {
                console.log("🎮 [WHEEL] Camera found, checking for enhanced PTZ wheel zoom")

                // Use enhanced PTZ wheel zoom for GridItemCamera
                if (gridItem.handleEnhancedWheelZoom && typeof gridItem.handleEnhancedWheelZoom === "function") {
                    console.log("🎮 [WHEEL] Using enhanced PTZ wheel zoom")
                    var handled = gridItem.handleEnhancedWheelZoom(wheel)
                    if (handled) {
                        wheel.accepted = true
                        return
                    }
                }

                console.log("🎮 [WHEEL] No enhanced wheel zoom available")
            }

            wheel.accepted = false
        }
    }

    function showContextMenu(x, y) {
        if (gridItem && gridItem.contextMenu) {
            gridItem.contextMenu.updateMenuForSelection()
            gridItem.contextMenu.popup(gridItem, x, y)
        }
    }

    function findMainGrid() {
        var current = gridItem ? gridItem.parent : null
        while (current) {
            if (current.hasOwnProperty("startItemDrag") && current.hasOwnProperty("updateItemDrag") && current.hasOwnProperty("endItemDrag")) {
                return current
            }
            current = current.parent
        }
        return null
    }

    function handleCloseButtonClick() {
        if (!gridItem || !gridItem.gridModel) {
            return false
        }
        var success = gridItem.gridModel.removeItemAt(gridItem.gridRow, gridItem.gridCol)
        return success
    }

    Connections {
        target: gridItem && gridItem.itemData ? gridItem.itemData : null
        function onIsAnimatingChanged() {
            if (gridItem.itemData.isAnimating) {
                dragArea.drag.target = undefined;
            }
        }
    }
}
