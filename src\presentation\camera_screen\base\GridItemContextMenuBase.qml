import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import models 1.0
import "../components"



Menu {
    id: contextMenu
    modal: true
    dim: false
    closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
    width: 200

    property var gridItem: null
    property var gridModel: null
    property int position: -1
    property string itemType: "camera"

    property string menuBackgroundColor: gridModel ? gridModel.get_color_theme_by_key("main_background") : "white"
    property string menuTextColor: gridModel ? gridModel.get_color_theme_by_key("text_color_all_app") : "white"
    property string menuHoverColor: gridModel ? gridModel.get_color_theme_by_key("primary") : "white"
    property string menuSeparatorColor: gridModel ? gridModel.get_color_theme_by_key("divider") : "white"
    property string menuBorderColor: gridModel ? gridModel.get_color_theme_by_key("common_border") : "white"
    property string icon_ai_flow: gridModel.get_image_theme_by_key("icon_ai_flow")
    property string icon_full_screen: gridModel.get_image_theme_by_key("icon_full_screen")
    property string icon_open_cam: gridModel.get_image_theme_by_key("icon_open_cam")
    property string icon_setting: gridModel.get_image_theme_by_key("icon_setting")
    property string icon_video_stream: gridModel.get_image_theme_by_key("icon_video_stream")
    property string icon_remove_cam: gridModel.get_image_theme_by_key("icon_remove_cam")

    property var selectedPositions: []
    property int selectedCount: 0
    property bool isMultiSelection: false

    Connections {
        target: gridModel
        function onThemeChanged() {
            menuBackgroundColor = gridModel.get_color_theme_by_key("main_background")
            menuTextColor = gridModel.get_color_theme_by_key("text_color_all_app")
            menuHoverColor = gridModel.get_color_theme_by_key("primary")
            menuSeparatorColor = gridModel.get_color_theme_by_key("divider")
            menuBorderColor = gridModel.get_color_theme_by_key("common_border")

            icon_open_cam = gridModel.get_image_theme_by_key("icon_open_cam")
            icon_remove_cam = gridModel.get_image_theme_by_key("icon_remove_cam")
            icon_setting = gridModel.get_image_theme_by_key("icon_setting")
            icon_full_screen = gridModel.get_image_theme_by_key("icon_full_screen")
            icon_ai_flow = gridModel.get_image_theme_by_key("icon_ai_flow")
            icon_video_stream = gridModel.get_image_theme_by_key("icon_video_stream")
        }
    }

    background: Rectangle {
        color: menuBackgroundColor
        border.color: menuBorderColor
        border.width: 1
    }

    function updateMenuForSelection() {
        selectedPositions = []
        selectedCount = 0

        if (gridModel && gridModel.getSelectedItems) {
            var selectedItems = gridModel.getSelectedItems()
            selectedCount = selectedItems.length

            for (var i = 0; i < selectedItems.length; i++) {
                var item = selectedItems[i]
                if (item && typeof item.row !== 'undefined' && typeof item.col !== 'undefined') {
                    var rowColObj = {row: item.row, col: item.col}
                    selectedPositions.push(rowColObj)
                }
            }
        }

        isMultiSelection = selectedCount > 1
        updateMenuItemsForSelection()
    }

    function updateMenuItemsForSelection() {
        if (isMultiSelection) {
            if (deleteMenuItem) {
                deleteMenuItem.text = qsTr("Delete %1 cameras").arg(selectedCount) + "\tDel"
            }
        } else {
            if (deleteMenuItem) {
                deleteMenuItem.text = qsTr("Remove from view") + "\tDel"
            }
        }
    }


    delegate: MenuItem {
        id: menuItemDelegate
        implicitWidth: 200
        implicitHeight: 32

        background: Rectangle {
            color: menuBackgroundColor
            border.color: menuItemDelegate.hovered ? menuHoverColor : "transparent"
            border.width: 2
            radius: 2
        }

        contentItem: Text {
            text: menuItemDelegate.text
            color: menuTextColor
            font.pixelSize: 14
            leftPadding: 8
            rightPadding: 8
            verticalAlignment: Text.AlignVCenter
        }
    }

    Menu {
        id: openCameraMenu
        title: qsTr("Open camera to...")
        icon.source: icon_open_cam
        width: 200

        background: Rectangle {
            color: menuBackgroundColor
            border.color: menuBorderColor
            border.width: 1
            radius: 4
        }

        delegate: MenuItem {
            id: submenuItem
            implicitWidth: 200
            implicitHeight: 32

            background: Rectangle {
                color: menuBackgroundColor
                border.color: submenuItem.hovered ? menuHoverColor : "transparent"
                border.width: 2
                radius: 2
            }

            contentItem: Text {
                text: parent.text
                color: menuTextColor
                font.pixelSize: 14
                leftPadding: 8
                rightPadding: 8
                verticalAlignment: Text.AlignVCenter
            }
        }

        MenuItem {
            id: newScreenMenuItem
            text: qsTr("New screen")

            background: Rectangle {
                color: menuBackgroundColor
                border.color: newScreenMenuItem.hovered ? menuHoverColor : "transparent"
                border.width: 2
                radius: 2
            }

            contentItem: Text {
                text: newScreenMenuItem.text
                color: menuTextColor
                font.pixelSize: 14
                leftPadding: 8
                rightPadding: 8
                verticalAlignment: Text.AlignVCenter
            }

            onTriggered: {
                        console.log("Opening multiple cameras in new view for positions:", JSON.stringify(contextMenu.selectedPositions))
                        gridModel.openSelectedItemsInNewTab(contextMenu.selectedPositions)
            }
        }

        MenuItem {
            id: newSavedViewMenuItem
            text: qsTr("New saved screen")

            background: Rectangle {
                color: menuBackgroundColor
                border.color: newSavedViewMenuItem.hovered ? menuHoverColor : "transparent"
                border.width: 2
                radius: 2
            }

            contentItem: Text {
                text: newSavedViewMenuItem.text
                color: menuTextColor
                font.pixelSize: 14
                leftPadding: 8
                rightPadding: 8
                verticalAlignment: Text.AlignVCenter
            }

            onTriggered: {
                contextMenu.close();
                Qt.callLater(function() {
                    newSavedViewDialog.visible = true;
                });
            }
        }

        MenuItem {
            id: newVirtualWindowMenuItem
            text: qsTr("New virtual window")

            background: Rectangle {
                color: menuBackgroundColor
                border.color: newVirtualWindowMenuItem.hovered ? menuHoverColor : "transparent"
                border.width: 2
                radius: 2
            }

            contentItem: Text {
                text: newVirtualWindowMenuItem.text
                color: menuTextColor
                font.pixelSize: 14
                leftPadding: 8
                rightPadding: 8
                verticalAlignment: Text.AlignVCenter
            }

            onTriggered: {
                contextMenu.close();
                Qt.callLater(function() {
                    newVirtualWindowDialog.visible = true;
                });
            }
        }

        MenuSeparator {
            background: Rectangle {
                color: menuSeparatorColor
                height: 1
            }
        }

        // Menu {
        //     title: "Màn hình đã lưu"
        //     width: 200

        //     background: Rectangle {
        //         color: menuBackgroundColor
        //         border.color: menuBorderColor
        //         border.width: 1
        //         radius: 4
        //     }

        //     MenuItem {
        //         text: "Không có màn hình đã lưu"
        //         enabled: false

        //         background: Rectangle {
        //             color: menuBackgroundColor
        //             border.color: "transparent"
        //             border.width: 2
        //             radius: 2
        //         }

        //         contentItem: Text {
        //             text: parent.text
        //             color: menuTextColor
        //             font.pixelSize: 14
        //             leftPadding: 8
        //             rightPadding: 8
        //             verticalAlignment: Text.AlignVCenter
        //         }
        //     }
        // }

        // Menu {
        //     title: "Cửa sổ giả lập"
        //     width: 200

        //     background: Rectangle {
        //         color: menuBackgroundColor
        //         border.color: menuBorderColor
        //         border.width: 1
        //         radius: 4
        //     }

        //     MenuItem {
        //         text: "Không có cửa sổ giả lập"
        //         enabled: false

        //         background: Rectangle {
        //             color: menuBackgroundColor
        //             border.color: "transparent"
        //             border.width: 2
        //             radius: 2
        //         }

        //         contentItem: Text {
        //             text: parent.text
        //             color: menuTextColor
        //             font.pixelSize: 14
        //             leftPadding: 8
        //             rightPadding: 8
        //             verticalAlignment: Text.AlignVCenter
        //         }
        //     }
        // }
    }

    MenuSeparator {
        background: Rectangle {
            color: menuSeparatorColor
            height: 1
        }
    }

    MenuItem {
        id: deleteMenuItem
        text: qsTr("Remove from view") + "\tDel"

        background: Rectangle {
            color: menuBackgroundColor
            border.color: deleteMenuItem.hovered ? menuHoverColor : "transparent"
            border.width: 2
            radius: 2
        }

        contentItem: Row {
            spacing: 8
            leftPadding: 8
            rightPadding: 8

            Image {
                source: icon_remove_cam
                width: 16
                height: 16
                anchors.verticalCenter: parent.verticalCenter
                fillMode: Image.PreserveAspectFit
            }

            Text {
                text: deleteMenuItem.text
                color: menuTextColor
                font.pixelSize: 14
                anchors.verticalCenter: parent.verticalCenter
            }
        }

        onTriggered: {
            if (gridModel) {
                if (isMultiSelection) {
                    // ✅ OPTIMIZED: Collect all positions first
                    var freshSelectedItems = gridModel.getSelectedItems()
                    var freshPositions = []

                    for (var i = 0; i < freshSelectedItems.length; i++) {
                        var item = freshSelectedItems[i]
                        if (item && typeof item.row !== 'undefined' && typeof item.col !== 'undefined') {
                            var rowColObj = {row: item.row, col: item.col}
                            freshPositions.push(rowColObj)
                        }
                    }

                    // ✅ SINGLE BATCH CALL: O(n) instead of O(n²)
                    if (freshPositions.length > 0) {
                        gridModel.removeMultipleCamerasAt(freshPositions)
                    }
                } else {
                    // ✅ SINGLE ITEM: Remains optimized
                    if (gridItem && typeof gridItem.gridRow !== 'undefined' && typeof gridItem.gridCol !== 'undefined') {
                        gridModel.removeItemAt(gridItem.gridRow, gridItem.gridCol)
                    }
                }
            }
        }
    }

    MenuSeparator {
        background: Rectangle {
            color: menuSeparatorColor
            height: 1
        }
    }

    function insertCameraSpecificMenus() {
        // Override in GridItemContextMenuCamera
    }

    onAboutToShow: {
        updateMenuForSelection()
    }

    Component.onCompleted: {
        insertCameraSpecificMenus()
    }
}
