<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en_US">
<context>
    <name>AICameraZoneWidget</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="69"/>
        <source>Entry Counting Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="70"/>
        <source>Exit Counting Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="77"/>
        <source>Intrusion Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="117"/>
        <source>Note: Choose 4 points to form a Quadrilateral.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1114"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1244"/>
        <source>Saved Zone List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="144"/>
        <source>Enter zone name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="78"/>
        <source>Recognition Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="154"/>
        <source>Draw</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="158"/>
        <source>Clear</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="162"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="476"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="791"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1056"/>
        <source>Human</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="478"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="792"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1059"/>
        <source>Vehicle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="81"/>
        <source>Protection Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="976"/>
        <source>Edit Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="986"/>
        <source>Zone Name:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="992"/>
        <source>Device Access Control:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1173"/>
        <source>Zone Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1191"/>
        <source>Zone Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1179"/>
        <source>Recognition Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="323"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1140"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="71"/>
        <source>Entry Protection Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="72"/>
        <source>Exit Protection Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="425"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="539"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="544"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="549"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="639"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="644"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="649"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="655"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="660"/>
        <source>Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="425"/>
        <source>Failed to delete the zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="540"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="640"/>
        <source>Please choose Zone type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="545"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="645"/>
        <source>Zone name is empty</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="550"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="650"/>
        <source>Zone name is exist</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="656"/>
        <source>Please choose 4 points to form a Quadrilateral.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="661"/>
        <source>Please click on one of the four edges to determine the direction.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AIZoneDropDownDraw</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_zone_dropdown_draw.py" line="56"/>
        <location filename="../presentation/device_management_screen/widget/ai_zone_dropdown_draw.py" line="58"/>
        <source>Select Items</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_zone_dropdown_draw.py" line="57"/>
        <source>All Items Selected</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddCameraDialog</name>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="45"/>
        <source>ADD CAMERAS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="91"/>
        <source>Known Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="95"/>
        <source>Subnet Scan</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="122"/>
        <source>Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="122"/>
        <source>IP/Hostname/RTSP/UDP Link</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="126"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="353"/>
        <source>Port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="132"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="359"/>
        <source>Default</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="150"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="378"/>
        <source>Login</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="150"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="378"/>
        <source>Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="151"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="379"/>
        <source>Password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="156"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="656"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="721"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="966"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="200"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="423"/>
        <source>Please enter all required information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="214"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="441"/>
        <source>BRAND</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="214"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="441"/>
        <source>MODEL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="214"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="441"/>
        <source>ADDRESS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="215"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="442"/>
        <source>STATUS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="271"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="497"/>
        <source>Add to Groups:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="277"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="502"/>
        <source>Select group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="286"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="512"/>
        <source>Please choose a group to add cameras.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="313"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="539"/>
        <source>No Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="322"/>
        <source>Searching</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="346"/>
        <source>From IP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="346"/>
        <source>0.0.0.0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="347"/>
        <source>To IP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="347"/>
        <source>*********</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="123"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="350"/>
        <source>Onvif Port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="390"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="730"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="778"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="994"/>
        <source>Scan</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="547"/>
        <source>Scanning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="670"/>
        <source>Please enter correct IP or RTSP format</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="709"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="766"/>
        <source>Stop</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="946"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="948"/>
        <source>Add</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="946"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="948"/>
        <source>Devices to Group:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="983"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1008"/>
        <source>Devices</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="984"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1009"/>
        <source>Add 0 Devices to Group:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1088"/>
        <source>Added</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1091"/>
        <source>New</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddFloorDialog</name>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="24"/>
        <source>Building Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="30"/>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="31"/>
        <source>Floor Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="34"/>
        <source>Upload image (File png, jpg, jpeg)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="46"/>
        <source>Level</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="54"/>
        <source>Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="67"/>
        <source>Floor name cannot be empty</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="73"/>
        <source>Floor image cannot be empty</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddGroupDialog</name>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="535"/>
        <source>Group Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="555"/>
        <source>Selected:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="567"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="572"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="573"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="574"/>
        <source>AIBox</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="575"/>
        <source>Door</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="577"/>
        <source>Device Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="603"/>
        <source>No Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="611"/>
        <source>Searching</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="754"/>
        <source>Group name is required</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddRoleDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="645"/>
        <source>ADD NEW ROLE</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="681"/>
        <source>System Permissions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="683"/>
        <source>List Of Users</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="720"/>
        <source>Please enter the group name.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddUserDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="41"/>
        <source>ADD NEW USER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="63"/>
        <source>Avatar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="76"/>
        <source>Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="77"/>
        <source>Full Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="78"/>
        <source>System User Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="79"/>
        <source>Enter Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="81"/>
        <source>Enter Full Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="86"/>
        <source>Select Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="100"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="104"/>
        <source>Subsystem</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="101"/>
        <source>Password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="102"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="104"/>
        <source>The password must be a minimum of 6 characters and a maximum of 32 characters, including uppercase and lowercase letters, numbers, and special characters.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="108"/>
        <source>Enter Password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="114"/>
        <source>Active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="114"/>
        <source>In-active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="127"/>
        <source>Phone number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="128"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="132"/>
        <source>Position</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="129"/>
        <source>Gender</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="130"/>
        <source>Phone Number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="135"/>
        <source>Male</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="135"/>
        <source>Female</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="147"/>
        <source>Email</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="180"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="215"/>
        <source>Please enter a valid username.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="187"/>
        <source>Please enter a valid email address.
 </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="200"/>
        <source>Username is existing.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="208"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="230"/>
        <source>Email is existing.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="218"/>
        <source>Please enter the full name.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="221"/>
        <source>Please choose an user group.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="224"/>
        <source>Please choose a subsystem.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="227"/>
        <source>Please enter a valid password.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="237"/>
        <source>Please select an avatar image.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CalendarComboBox</name>
    <message>
        <location filename="../common/widget/event/calendar_combobox.py" line="20"/>
        <source>Time</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CalendarPickerWidget</name>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>January</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>February</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>March</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>April</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>May</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>June</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>July</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>August</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>September</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="54"/>
        <source>October</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="54"/>
        <source>November</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="55"/>
        <source>December</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CameraBottomToolbarWidget</name>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="430"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="438"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="446"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="454"/>
        <source>Divisions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="75"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="273"/>
        <source>Main</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="275"/>
        <source>Sub</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="126"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="521"/>
        <source>Record Video</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="135"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="523"/>
        <source>Capture</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="142"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="525"/>
        <source>Microphone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="149"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="527"/>
        <source>Volume</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="160"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="529"/>
        <source>Full screen</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="228"/>
        <source>Edit</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CameraGridWidget</name>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="114"/>
        <source>Please enlarge the grid cell or switch to a grid size smaller than 3x3.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="117"/>
        <source>To drag an item onto the Map, you need to enter Map edit mode.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="120"/>
        <source>Map saved successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="123"/>
        <source>Failed to save map.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="126"/>
        <source>This camera is already assigned to another position on the map.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="131"/>
        <source>Floor saved successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="134"/>
        <source>Failed to save floor.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CameraInfoDialog</name>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="112"/>
        <source>Camera Configuration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="114"/>
        <source>Recording Configuration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="130"/>
        <source>Camera Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="134"/>
        <source>Camera URL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="144"/>
        <source>Group Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="148"/>
        <source>Description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="152"/>
        <source>Latitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="156"/>
        <source>Longitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="165"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="173"/>
        <source>Find address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="174"/>
        <source>Find coordinates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="213"/>
        <source>Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="213"/>
        <source>Enter address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="175"/>
        <source>Open map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="40"/>
        <source>Camera Information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="395"/>
        <source>Latitude or Longitude is empty</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="399"/>
        <source>Finding address...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="402"/>
        <source>Latitude and Longitude must be a number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="410"/>
        <source>Finding coords...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="414"/>
        <source>Address is empty</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="418"/>
        <source>Finding address failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="425"/>
        <source>Finding coordinate failed</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CameraScreen</name>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="821"/>
        <source>This ShortcutID does not exist.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1039"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1041"/>
        <source>Virtual Window </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1053"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1055"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1081"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1083"/>
        <source>View </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1067"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1069"/>
        <source>Map </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1549"/>
        <source>Editing </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1577"/>
        <source>Editing digital map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1720"/>
        <source>No camera selected</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1902"/>
        <source>Open the left sidebar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1905"/>
        <source>Close the left sidebar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1916"/>
        <source>Open the event bar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1919"/>
        <source>Close the event bar</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ChangeModeButtonSideMenu</name>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="27"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="31"/>
        <source>Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="35"/>
        <source>Camera Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="39"/>
        <source>Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="43"/>
        <source>Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="47"/>
        <source>Map</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ChildEventWidget</name>
    <message>
        <location filename="../common/widget/event_bar.py" line="1069"/>
        <source>Loading...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ComboBoxUnit</name>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="853"/>
        <source>Second</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="854"/>
        <source>Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="855"/>
        <source>Hour</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ComboBoxWithRequireField</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2259"/>
        <source>Choose one</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ConfigCameraFovDialog</name>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="103"/>
        <source>Camera Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="110"/>
        <source>Size</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="146"/>
        <source>Small</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="166"/>
        <source>Medium</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="185"/>
        <source>Large</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="212"/>
        <source>Show as:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="225"/>
        <source>Icon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="258"/>
        <source>Shape</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="405"/>
        <source>Color</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="581"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="461"/>
        <source>Preferences</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="507"/>
        <source>Show field of view</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="555"/>
        <source>Show name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="564"/>
        <source>Redraw</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ContentAddUpdateScriptAIDialog</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="78"/>
        <source>EDIT AI SCRIPT</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="79"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="80"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="81"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="82"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="83"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="84"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="85"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="86"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="170"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="299"/>
        <source>*</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="365"/>
        <source>AI Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="140"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="379"/>
        <source>Update frequency:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="380"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="403"/>
        <source>Seconds</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="138"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="383"/>
        <source>Confidence threshold:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="143"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="388"/>
        <source>Tracking time:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="388"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="398"/>
        <source>Minutes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="145"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="393"/>
        <source>Appearance count:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="393"/>
        <source>Times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="148"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="398"/>
        <source>Disappear time:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="150"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="403"/>
        <source>Standing time:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="438"/>
        <source>Draw active zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="599"/>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="611"/>
        <source>Update frequency must be greater than zero or equal to zero.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="617"/>
        <source>Confidence threshold must be between 0 and 100.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="624"/>
        <source>Tracking time must be greater than zero.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="629"/>
        <source>Appearance count must be greater than zero.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="635"/>
        <source>Disappear time must be greater than zero.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="641"/>
        <source>Standing time must be greater than zero.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CreateBuildingDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3416"/>
        <source>Create Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3419"/>
        <source>Edit Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3406"/>
        <source>Building name</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CreateFloorDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3370"/>
        <source>Name floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3383"/>
        <source>Create floor</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CustomComboBox</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="648"/>
        <source>Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="678"/>
        <source>Please select</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="681"/>
        <source>High (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="684"/>
        <source>Medium</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="687"/>
        <source>Low</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="690"/>
        <source>x1 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="693"/>
        <source>15 minutes (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="696"/>
        <source>20 minutes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="699"/>
        <source>25 minutes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="702"/>
        <source>56 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="705"/>
        <source>0.5 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="708"/>
        <source>0.48 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="711"/>
        <source>21 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="714"/>
        <source>5 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="717"/>
        <source>10 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="720"/>
        <source>20 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="723"/>
        <source>30 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="726"/>
        <source>40 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="729"/>
        <source>50 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="732"/>
        <source>60 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="735"/>
        <source>70 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="738"/>
        <source>80 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="741"/>
        <source>90 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="744"/>
        <source>100 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="747"/>
        <source>200 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="750"/>
        <source>300 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="753"/>
        <source>400 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="756"/>
        <source>500 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CustomMenuForEventRightClick</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="346"/>
        <source>New View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="353"/>
        <source>New Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="359"/>
        <source>New Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CustomMenuWithCheckbox</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="107"/>
        <source>Main</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="112"/>
        <source>Sub</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="117"/>
        <source>AI</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CustomVideoOutput</name>
    <message>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="10"/>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="56"/>
        <source>No Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="38"/>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="46"/>
        <source>Connecting</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="56"/>
        <source>Disconnected</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DeviceController</name>
    <message>
        <location filename="../common/qml/models/device_controller.py" line="573"/>
        <source>The camera is recording. Are you sure you want to delete?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/models/device_controller.py" line="612"/>
        <source>Edit group</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DeviceGroupTable</name>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="60"/>
        <source>Box</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="69"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="164"/>
        <source>Box (0)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="173"/>
        <source>Camera (0)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="274"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="629"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="939"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="285"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="641"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="951"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="296"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="653"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="963"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="307"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="665"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="975"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="318"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="677"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="987"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="329"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="689"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="999"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="399"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="759"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1051"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="388"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="747"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1039"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="51"/>
        <source>Group name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="87"/>
        <source>Action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="259"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="614"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="929"/>
        <source>Recognition &amp; Protection (%1)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="372"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="732"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1031"/>
        <source>Risk Recognition (%1)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="472"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="819"/>
        <source>Number of AI selected</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DeviceTable</name>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="54"/>
        <source>Device name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="63"/>
        <source>Branch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="72"/>
        <source>Model</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="81"/>
        <source>IP address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="90"/>
        <source>Mac address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="99"/>
        <source>Partner</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="108"/>
        <source>Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="117"/>
        <source>Action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="320"/>
        <source>Recognition &amp; Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="358"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="371"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="384"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="397"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="410"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="423"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="468"/>
        <source>Risk Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="520"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="507"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DialogEditGridLayouts</name>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="78"/>
        <source>EDIT GRID LAYOUTS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="110"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="121"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="142"/>
        <source>Columns: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="143"/>
        <source>Rows: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="176"/>
        <source>Layouts:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="212"/>
        <source>Restore to Default</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="266"/>
        <source> Divisions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="288"/>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="296"/>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="303"/>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="311"/>
        <source>Divisions</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>EventBar</name>
    <message>
        <location filename="../common/widget/event_bar.py" line="119"/>
        <location filename="../common/widget/event_bar.py" line="613"/>
        <source>Realtime Events</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="125"/>
        <location filename="../common/widget/event_bar.py" line="616"/>
        <source>Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="150"/>
        <source>Filter</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="158"/>
        <source>refresh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="270"/>
        <location filename="../common/widget/event_bar.py" line="619"/>
        <source>No search results</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>No results found</source>
        <translation type="vanished">No results found</translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="405"/>
        <location filename="../common/widget/event_bar.py" line="413"/>
        <location filename="../common/widget/event_bar.py" line="422"/>
        <location filename="../common/widget/event_bar.py" line="431"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>EventItem</name>
    <message>
        <location filename="../common/widget/event_bar.py" line="821"/>
        <source>Undefined</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="824"/>
        <source>Appears at </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/warning_alert_camera_widget.py" line="80"/>
        <source>Traffic Vehicle Detection Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/warning_alert_camera_widget.py" line="82"/>
        <source>Crowd Detected Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/warning_alert_camera_widget.py" line="84"/>
        <source>Intruder Detected Warning</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>EventWidget</name>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="262"/>
        <source>AI Flows: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="270"/>
        <source>Name: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="283"/>
        <source>Status: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="295"/>
        <source>Camera: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="323"/>
        <source>Time: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="442"/>
        <source>No group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="483"/>
        <source>Car</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="485"/>
        <source>Motor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="487"/>
        <source>Bicycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="489"/>
        <source>Pedestrian</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="491"/>
        <source>Truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="493"/>
        <source>Bus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="495"/>
        <source>Van</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="497"/>
        <source>Container truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="499"/>
        <source>Delivery tricycles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="501"/>
        <source>Cyclo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="503"/>
        <source>Ambulance</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="505"/>
        <source>Fire truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="507"/>
        <source>Wheelchair</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="509"/>
        <source>Trash car</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="511"/>
        <source>Tank truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="513"/>
        <source>Mixer truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="515"/>
        <source>Crane</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="517"/>
        <source>Roller</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="519"/>
        <source>Excavator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="521"/>
        <source>Street Vendor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="524"/>
        <location filename="../common/widget/event/event_widget.py" line="542"/>
        <source>Undefined</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="527"/>
        <source>Black</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="529"/>
        <source>White</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="531"/>
        <source>Red</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="533"/>
        <source>Blue</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="535"/>
        <source>Green</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="537"/>
        <source>Yellow</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="539"/>
        <source>Orange</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FilterButtonSideMenu</name>
    <message>
        <location filename="../common/widget/custom_filter.py" line="47"/>
        <source>AI flows</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="49"/>
        <source>State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="55"/>
        <source>Connected</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="56"/>
        <source>Disconnected</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="57"/>
        <source>Face Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="59"/>
        <source>Vehicle</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FilterEventDialog</name>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="97"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="104"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="122"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="137"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="155"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="162"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="163"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="164"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="165"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="105"/>
        <source>Check-In</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="106"/>
        <source>Check-Out</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="107"/>
        <source>Appear</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="115"/>
        <source>Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="130"/>
        <source>AI Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="138"/>
        <source>Human</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="139"/>
        <source>Vehicle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="140"/>
        <source>Crowd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="148"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FooterDialog</name>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="248"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="251"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="254"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="260"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="263"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="266"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="249"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="268"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="252"/>
        <source>Update</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="255"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="258"/>
        <source>Create</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="257"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="261"/>
        <source>Connect</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="264"/>
        <source>Ok</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FooterInDialog</name>
    <message>
        <location filename="../common/widget/dialogs/footer_widget.py" line="29"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/footer_widget.py" line="31"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/footer_widget.py" line="50"/>
        <location filename="../common/widget/dialogs/footer_widget.py" line="54"/>
        <source>Update</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FrameModel</name>
    <message>
        <location filename="../common/qml/models/frame_model.py" line="751"/>
        <source>Clear Selection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/models/frame_model.py" line="753"/>
        <source>Zoom to Selection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/models/frame_model.py" line="755"/>
        <source>Export video</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>GeneralSettingTab</name>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="36"/>
        <source>1. Language:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="43"/>
        <source>2. Personalize:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="50"/>
        <source>3. Dialog Settings:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="64"/>
        <source>Auto</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="68"/>
        <source>Light Theme</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="72"/>
        <source>Dark Theme</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="115"/>
        <source>English</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="124"/>
        <source>Vietnamese</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="133"/>
        <source>Russian</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="216"/>
        <source>Reset all dialog settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="223"/>
        <source>Reset all &apos;Don&apos;t show again&apos; settings for confirmation dialogs.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="242"/>
        <source>All dialog settings have been reset.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>GroupTable</name>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="276"/>
        <source>No</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="277"/>
        <source>Device name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="278"/>
        <source>IP address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="279"/>
        <source>Mac address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="280"/>
        <source>Group</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ImageAdjWidget</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="107"/>
        <source>Brightness</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="109"/>
        <source>Sharpness</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="111"/>
        <source>Contrast</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="113"/>
        <source>Saturation</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ImageAvatarDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="615"/>
        <source>AVATAR</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ImageWidget</name>
    <message>
        <location filename="../common/widget/image_widget.py" line="132"/>
        <source>Loading...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>InputCallbackWithMessage</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1795"/>
        <source>Show menu</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>InputWithDataCallback</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1526"/>
        <source>Show menu</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>InputWithRequireField</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2090"/>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2175"/>
        <source>Show password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2171"/>
        <source>Hide password</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>InputWithTitle</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1673"/>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1693"/>
        <source>Show password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1677"/>
        <source>Show menu</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1689"/>
        <source>Hide password</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ItemGridCustom</name>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/list_item_grid_custom.py" line="140"/>
        <source>Divisions</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ItemNotification</name>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="649"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="653"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="835"/>
        <source>Face</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="656"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="837"/>
        <source>License plate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="659"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="839"/>
        <source>Access control</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="663"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="843"/>
        <source>Crowd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="760"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="843"/>
        <source>Warning context: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="778"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="854"/>
        <source>Whenever Vehicle Appears</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="783"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="859"/>
        <source>Whenever Human Appears</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="787"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="863"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="790"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="866"/>
        <source>Appears </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="791"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="867"/>
        <source>times within </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="791"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="867"/>
        <source>hour</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="793"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="869"/>
        <source>Warning Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="799"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="875"/>
        <source>Counting &amp; Warning Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="803"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="879"/>
        <source>Counting</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="805"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="881"/>
        <source>Quantity: </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LabelLineEdit</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1338"/>
        <source>Exp: 5, 13, 202,...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ListenShowNotification</name>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="131"/>
        <source>UNKNOWN</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="132"/>
        <source>An internal server error occurred.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="133"/>
        <source>Camera not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="134"/>
        <source>The camera is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="135"/>
        <source>The camera name cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="136"/>
        <source>This camera name already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="137"/>
        <source>The camera URL cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="138"/>
        <source>This camera URL already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="139"/>
        <source>The camera mainstream URL is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="140"/>
        <source>The camera substream URL is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="141"/>
        <source>The camera&apos;s supported main resolution is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="142"/>
        <source>The camera&apos;s supported main FPS is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="143"/>
        <source>The camera&apos;s supported sub resolution is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="144"/>
        <source>The camera&apos;s supported sub FPS is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="145"/>
        <source>The camera status cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="146"/>
        <source>The camera state cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="147"/>
        <source>Camera group not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="148"/>
        <source>The camera group is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="149"/>
        <source>Invalid parent-child relationship in camera group.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="150"/>
        <source>The camera group name cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="151"/>
        <source>This camera group name already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="152"/>
        <source>The camera group parent ID does not exist.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="153"/>
        <source>The camera group child ID does not exist.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="154"/>
        <source>Event not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="155"/>
        <source>The event is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="156"/>
        <source>The event profile ID cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="157"/>
        <source>The event creation time cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="158"/>
        <source>The event image URL cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="159"/>
        <source>The event video URL cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="160"/>
        <source>Metadata cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="161"/>
        <source>The metadata is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="162"/>
        <source>Profile not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="163"/>
        <source>The profile name cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="164"/>
        <source>The profile is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="165"/>
        <source>This profile name already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="166"/>
        <source>This profile UUID already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="167"/>
        <source>AI flow not found, please press button to create AI and set zone for this AI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="168"/>
        <source>The AI flow name cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="169"/>
        <source>The AI flow type cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="170"/>
        <source>The AI flow apply field cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="171"/>
        <source>The AI flow is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="172"/>
        <source>The date format is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="173"/>
        <source>The username cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="174"/>
        <source>This username already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="175"/>
        <source>The password cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="176"/>
        <source>The user is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="177"/>
        <source>The token is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="178"/>
        <source>User not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="179"/>
        <source>The password is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="180"/>
        <source>API key not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="181"/>
        <source>The camera ID cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="182"/>
        <source>The camera group ID cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="183"/>
        <source>AI flow is not applied, please set zone for this AI flow</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="186"/>
        <source>Unable to connect to server.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="48"/>
        <source>Add Camera Successfully Using RTSP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="69"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="71"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="73"/>
        <source>Successfully Added Camera List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="75"/>
        <source>Add Camera Successfully Using Onvif</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="49"/>
        <source>Update Camera to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="53"/>
        <source>Successfully deleted Camera Group on Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="51"/>
        <source>Added Camera Group to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="50"/>
        <source>Successfully deleted Camera on Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="52"/>
        <source>Update Camera Group to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="54"/>
        <source>Added AIFlow to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="55"/>
        <source>Update AIFlow to Server Successfully</source>
        <translation type="unfinished">Update AI to Server Successfully</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="56"/>
        <source>There is no AI problem</source>
        <translation type="unfinished">Need to configure AI and draw the area before turning on.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="57"/>
        <source>Successfully deleted AIFlow on Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="58"/>
        <source>Added ShortcutID to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="59"/>
        <source>Update ShortcutID to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="60"/>
        <source>Successfully deleted ShortcutID on Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="61"/>
        <source>Update Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="65"/>
        <source>Successfully completed apply AI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="88"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="120"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="123"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="126"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="188"/>
        <source>An error occurred</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="92"/>
        <source>No response received</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LoadingDataDialog</name>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="619"/>
        <source>Connecting...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LoginDialog</name>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="119"/>
        <source>Failed to load captcha. Please try again later.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="127"/>
        <source>Unable to connect to captcha service. Please check your network connection.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="141"/>
        <source>Add Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="144"/>
        <source>Server IP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="144"/>
        <source>Server IP: *************</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="146"/>
        <source>Server Port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="146"/>
        <source>Enter server port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="148"/>
        <source>Event Port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="148"/>
        <source>Enter event port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="150"/>
        <source>Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="150"/>
        <source>Enter username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="152"/>
        <source>Password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="152"/>
        <source>Enter password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="166"/>
        <source>Show password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="174"/>
        <source>Change</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="176"/>
        <source>Login</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="181"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="273"/>
        <source>Please enter a address Server.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="276"/>
        <source>Please enter a server port.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="279"/>
        <source>Please enter a websocket port.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="282"/>
        <source>Please enter a username.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="285"/>
        <source>Please enter a password.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="292"/>
        <location filename="../presentation/server_screen/login_dialog.py" line="340"/>
        <source>This Server already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="335"/>
        <source>This Server is connected. Please disconnect and login again.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="372"/>
        <source>Failed to connect to Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="375"/>
        <source>This account is not allowed to log in to the system.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="378"/>
        <source>Username or password is incorrect!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LoginForm</name>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="67"/>
        <source>Intelligent Surveillance Platform</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="157"/>
        <source>Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="163"/>
        <location filename="../presentation/auth_screen/login_screen.py" line="276"/>
        <source>Show password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="168"/>
        <source>Password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="176"/>
        <source>Forgot your password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="177"/>
        <source>Sign in with SSO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="182"/>
        <source>LOGIN</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="240"/>
        <source>CONNECT</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="272"/>
        <source>Hide password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="297"/>
        <source>Please enter a username.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="300"/>
        <source>Please enter a password.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/auth_screen/login_screen.py" line="310"/>
        <source>Username or password is incorrect!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MainScreen</name>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="71"/>
        <source>Processing...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="71"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="319"/>
        <source>Add Camera Successfully Using RTSP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="322"/>
        <source>Update Camera to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="325"/>
        <location filename="../presentation/main_screen/main_screen.py" line="334"/>
        <source>Successfully deleted Camera Group on Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="328"/>
        <source>Added Camera Group to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="331"/>
        <source>Update Camera Group to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="337"/>
        <source>Update Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="350"/>
        <source>Successfully Updated Map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="353"/>
        <source>Update map fail</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="356"/>
        <source>Start edit map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="359"/>
        <source>End edit map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="363"/>
        <source>Please enable edit map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="366"/>
        <source>Open camera success</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="369"/>
        <source>Open camera failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="372"/>
        <source>Camera exist in map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="375"/>
        <source>Saved successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="378"/>
        <source>Failed to save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="381"/>
        <source>The map data has been updated.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="384"/>
        <source>Camera already have location</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MainTreeViewWidget</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="85"/>
        <source>Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="86"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3000"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3118"/>
        <source>Remove</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="125"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3220"/>
        <source>Camera List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="128"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3224"/>
        <source>Virtual Window List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="129"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3228"/>
        <source>Saved View List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="130"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3232"/>
        <source>Map List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="422"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="474"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1271"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2265"/>
        <source>This screen already contains a virtual window named</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="424"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="476"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1272"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2267"/>
        <source>Do you want to replace this virtual window?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1099"/>
        <source>No search results</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1160"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1162"/>
        <source>Virtual Window </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1174"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1176"/>
        <source>View </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1310"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1325"/>
        <source>This Shortcut id already exists</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1328"/>
        <source>Please Enter Complete Information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1618"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1718"/>
        <source>Trường hợp này xử lý sau khi refactor code</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1789"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1887"/>
        <source>This Virtual Screen already exists</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1845"/>
        <source>This saved view already exists</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1953"/>
        <source>Delete Floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1953"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2091"/>
        <source>Confirm</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1953"/>
        <source>Are you sure you want to delete this floor?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1962"/>
        <source>Delete floor successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1996"/>
        <source>Create Floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2028"/>
        <source>Create floor failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2052"/>
        <source>Updated successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2055"/>
        <source>Failed to update the building.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2091"/>
        <source>Delete Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2091"/>
        <source>Are you sure you want to delete this building?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2108"/>
        <source>Failed to delete the building.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1948"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1990"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2035"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2084"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2145"/>
        <source>The map data has been updated.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1965"/>
        <source>Failed to delete the floor.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2013"/>
        <source>Failed to add the floor.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2103"/>
        <source>Building deleted successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2167"/>
        <source>Building added successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2170"/>
        <source>Failed to add the building.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2499"/>
        <source>Edit group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2619"/>
        <source>Turn off AI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2620"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2621"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2622"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2623"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2624"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2625"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2626"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2627"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2642"/>
        <source>Stream group in</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2644"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2779"/>
        <source>Delete	Del</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2647"/>
        <source>Remove from Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2649"/>
        <source>Rename	F2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2652"/>
        <source>AI flow</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2654"/>
        <source>Setting	Ctrl+I</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2674"/>
        <source>Open camera to ...    </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2676"/>
        <source>Add camera to group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2791"/>
        <source>Open cameras to ...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2808"/>
        <source>Add cameras to group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2838"/>
        <source>Exit Streaming Camera </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2848"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3007"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3125"/>
        <source>Shortcut ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2852"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3011"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3129"/>
        <source>Set Shortcut ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2893"/>
        <source>Exit streaming group </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2910"/>
        <source>Add Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2914"/>
        <source>Remove All Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2918"/>
        <source>Close All Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2935"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3059"/>
        <source>Open</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2967"/>
        <source>Switch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2996"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3113"/>
        <source>Rename</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3019"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3107"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3029"/>
        <source>Add Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3033"/>
        <source>Open All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3037"/>
        <source>Remove All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3041"/>
        <source>Close All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3063"/>
        <source>New Tab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3076"/>
        <source>Create Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3144"/>
        <source>Open digital map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3146"/>
        <source>Edit Map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3151"/>
        <source>Create Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3155"/>
        <source>Remove all</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3167"/>
        <source>Edit Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3171"/>
        <source>Create floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3175"/>
        <source>Remove building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3189"/>
        <source>Open floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3191"/>
        <source>Edit floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3195"/>
        <source>Remove floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3204"/>
        <source>Create group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3209"/>
        <source>Connect to third-party server</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Map2DCameraItem</name>
    <message>
        <location filename="../common/qml/map/Map2DCameraItem.qml" line="370"/>
        <source>Back</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Map2DWidgetItem</name>
    <message>
        <location filename="../presentation/camera_screen/map/map_widget_item.py" line="74"/>
        <source>Floor saved successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/map/map_widget_item.py" line="77"/>
        <source>Failed to save floor.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/map/map_widget_item.py" line="82"/>
        <source>To drag an item onto the Map, you need to enter edit mode.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MapGridWidgetItem</name>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/map_grid_item_widget.py" line="96"/>
        <source>Please enlarge the grid cell or switch to a grid size smaller than 3x3.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/map_grid_item_widget.py" line="99"/>
        <source>To drag an item onto the Map, you need to enter edit mode.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/map_grid_item_widget.py" line="102"/>
        <source>Map saved successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/map_grid_item_widget.py" line="105"/>
        <source>Failed to save map.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/map_grid_item_widget.py" line="108"/>
        <source>This camera is already assigned to another position on the map.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MapItem</name>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="100"/>
        <source>Name:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="101"/>
        <source>Location:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="128"/>
        <source>Remove Camera from Map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="128"/>
        <source>Remove Building from Map</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MapScreen</name>
    <message>
        <location filename="../presentation/map_screen/map_screen.py" line="18"/>
        <source>Map Screen</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/map_screen/map_screen.py" line="21"/>
        <source>Get</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/map_screen/map_screen.py" line="25"/>
        <source>Update</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/map_screen/map_screen.py" line="29"/>
        <source>Button 3</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MultiDropDownDialog</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/multidropdown.py" line="323"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>NewCustomTabWidget</name>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="83"/>
        <source>New Tab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="86"/>
        <source>Open Tab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="129"/>
        <source>Add to Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="135"/>
        <source>Add to Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="158"/>
        <source>Save as...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="165"/>
        <source>Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="167"/>
        <source>Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="256"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="258"/>
        <source>Virtual Window </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="270"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="272"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="284"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="286"/>
        <source>View </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>NewTabWidget</name>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="908"/>
        <source>Close Tab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="911"/>
        <source>Close All Tabs</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>NewVirtualWindowDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3324"/>
        <source>Saved View Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3327"/>
        <source>Virtual Window Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3335"/>
        <source>Create</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3337"/>
        <source>Add Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3339"/>
        <source>Add Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3342"/>
        <source>Edit Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>PTZ_Dropdow</name>
    <message>
        <location filename="../common/widget/ptz_widget.py" line="976"/>
        <location filename="../common/widget/ptz_widget.py" line="980"/>
        <source>Please select Camera.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/ptz_widget.py" line="984"/>
        <source>This camera does not support PTZ.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>PreviewItem</name>
    <message>
        <location filename="../common/qml/map/PreviewItem.qml" line="372"/>
        <source>No floor plan available</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RecursiveTreeview</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2755"/>
        <source>ALL</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RoleInfoDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="802"/>
        <source>ROLE INFORMATION</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="852"/>
        <source>System Permissions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="854"/>
        <source>List Of Users</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="894"/>
        <source>Please enter the group name.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ScheduleUI</name>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="23"/>
        <source>Recording</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="109"/>
        <source>Record Always</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="158"/>
        <source>Do Not Record</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="181"/>
        <source>Copy Schedule to...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="230"/>
        <source>ALL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="260"/>
        <source>Mon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="260"/>
        <source>Tue</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="260"/>
        <source>Wed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="260"/>
        <source>Thu</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="260"/>
        <source>Fri</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="260"/>
        <source>Sat</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="260"/>
        <source>Sun</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="444"/>
        <source>Keep Archive for...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="454"/>
        <source>Max</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="550"/>
        <source>Days</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="550"/>
        <source>Months</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="655"/>
        <source>Auto</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="683"/>
        <source>Schedule Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="786"/>
        <source>Quality</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="796"/>
        <source>Low</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="796"/>
        <source>Medium</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="796"/>
        <source>High</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="796"/>
        <source>Best</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ScreenTable</name>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="226"/>
        <source>NO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="227"/>
        <source>SCREEN NAME</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="228"/>
        <source>SELECT</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Search</name>
    <message>
        <location filename="../common/qml/videoplayback/Search.qml" line="29"/>
        <source>Search...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SearchBar</name>
    <message>
        <location filename="../common/widget/search_widget/search_bar.py" line="24"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SearchWidget</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="358"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SelectCamerasDialog</name>
    <message>
        <location filename="../common/qml/models/recording_schedule.py" line="705"/>
        <source>Select Cameras</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/SelectCamerasDialog.qml" line="81"/>
        <source>No search results</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SelectTabWidget</name>
    <message>
        <location filename="../common/qml/map/SelectTabWidget.qml" line="27"/>
        <source>Select tab to move</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/SelectTabWidget.qml" line="60"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/SelectTabWidget.qml" line="76"/>
        <source>Move</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ServerItem</name>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="142"/>
        <location filename="../presentation/server_screen/server_item.py" line="209"/>
        <location filename="../presentation/server_screen/server_item.py" line="357"/>
        <source>Connect</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="182"/>
        <location filename="../presentation/server_screen/server_item.py" line="355"/>
        <source>Disconnect</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="304"/>
        <source>Failed to connect to Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="318"/>
        <source>User information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="322"/>
        <source>Change password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="325"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SettingScreen</name>
    <message>
        <location filename="../presentation/setting_screen/setting_screen.py" line="33"/>
        <source>General</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/setting_screen.py" line="35"/>
        <source>Tracking configuration</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ShortcutIDDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3443"/>
        <source>Shortcut ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3452"/>
        <source>Add Shortcut ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3454"/>
        <source>Edit Shortcut ID</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SquareButton</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1106"/>
        <source>Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1113"/>
        <source>Standard Window Division</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1114"/>
        <source>Custom Window Division</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1207"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1209"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1211"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1213"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1215"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1217"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1219"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1221"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SubMenuAIFlow</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="302"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="304"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="306"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="308"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="310"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="312"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="314"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="316"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SubMenuCreateObject</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="706"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="711"/>
        <source>Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="716"/>
        <source>Floor</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SubMenuOpenCameraInTab</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="570"/>
        <source>New View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="576"/>
        <source>New Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="582"/>
        <source>New Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TabDeviceWidget</name>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="291"/>
        <source>Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="312"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="225"/>
        <source>Device Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="226"/>
        <source>Device</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="349"/>
        <source>Add Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="559"/>
        <source>ADD TRACING SCRIPT</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="285"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="367"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="255"/>
        <source>Add Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="286"/>
        <source>Enter Camera Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="295"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="296"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="301"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="311"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="373"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="296"/>
        <source>Online</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="296"/>
        <source>Offline</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="302"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="374"/>
        <source>Face</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="303"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="375"/>
        <source>Vehicle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="307"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="379"/>
        <source>AI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="313"/>
        <source>AIBox</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="314"/>
        <source>Door</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="316"/>
        <source>Device Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="368"/>
        <source>Enter Group Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="574"/>
        <source>ADD CAMERA</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="578"/>
        <source>ADD INTEGRATED DEVICE</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="604"/>
        <source>This feature is under development</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="625"/>
        <source>Successfully Downloaded File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="627"/>
        <source>File Download Failed</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TabWidget</name>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="94"/>
        <source>Logout</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="95"/>
        <source>Change Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="157"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="180"/>
        <source>Close Tab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="160"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="183"/>
        <source>Close All Tabs</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TimePickerWidget</name>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="331"/>
        <source>Start time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="333"/>
        <source>End time</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TrackingSettingTab</name>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="377"/>
        <source>Tracking configuration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="405"/>
        <source>Show tracking screen</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="408"/>
        <source>Yes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="412"/>
        <source>No</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="419"/>
        <source>Screen tracking list</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="435"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="462"/>
        <source>Saved successfully.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TreeViewWidget</name>
    <message>
        <source>No results found</source>
        <translation type="obsolete">No results found</translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="337"/>
        <source>No search results</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="651"/>
        <location filename="../common/widget/tree_view_widget.py" line="708"/>
        <location filename="../common/widget/tree_view_widget.py" line="956"/>
        <location filename="../common/widget/tree_view_widget.py" line="1010"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="874"/>
        <source>Exit Streaming Camera </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="878"/>
        <source>Choose position to stream on grid</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="888"/>
        <source>Exit Streaming Group </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="895"/>
        <source>Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="897"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>UserGroupsTableView</name>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>NO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>USER GROUP NAME</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>DESCRIPTION</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>STATUS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="50"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="317"/>
        <source>ACTIONS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="123"/>
        <source>Total: 50</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="125"/>
        <source>Show records/page: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="214"/>
        <source>Total: </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>UserInformationDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="329"/>
        <source>USER DETAIL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="351"/>
        <source>Avatar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="368"/>
        <source>Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="369"/>
        <source>Full Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="370"/>
        <source>System User Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="374"/>
        <source>Enter Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="377"/>
        <source>Enter Full Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="383"/>
        <source>Select Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="398"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="400"/>
        <source>Subsystem</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="399"/>
        <source>Phone number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="400"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="405"/>
        <source>Active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="405"/>
        <source>In-active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="408"/>
        <source>Phone Number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="422"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="427"/>
        <source>Position</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="423"/>
        <source>Gender</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="424"/>
        <source>Email</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="431"/>
        <source>Male</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="431"/>
        <source>Female</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="493"/>
        <source>Please enter the full name.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="496"/>
        <source>Please choose an user group.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="499"/>
        <source>Please choose a subsystem.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="502"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="560"/>
        <source>Email is existing.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="509"/>
        <source>Please select an avatar image.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="539"/>
        <source>Please enter a valid email address.
 </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="552"/>
        <source>Username is existing.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>UserPermissionsWidget</name>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="217"/>
        <source>Users Management</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="218"/>
        <source>User Groups Management</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="239"/>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="287"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="288"/>
        <source>Search by name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="250"/>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="298"/>
        <source>Refresh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="240"/>
        <source>Search by name, email, phone number, group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="256"/>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="302"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="262"/>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="306"/>
        <source>ADD</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>UsersTableView</name>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>NO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>USERNAME</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>FULLNAME</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>EMAIL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>PHONE NUMBER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>GROUP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>STATUS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="53"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="389"/>
        <source>ACTIONS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="143"/>
        <source>Total: 50</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="145"/>
        <source>Show records/page: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="236"/>
        <source>Total: </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WarningDialog</name>
    <message>
        <location filename="../common/widget/dialogs/warning_dialog.py" line="28"/>
        <source>Are you sure you want to delete?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/warning_dialog.py" line="53"/>
        <source>Notification</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WarningEventDialog</name>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="302"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="465"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="304"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="466"/>
        <source>Unread</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WarningListView</name>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="520"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="610"/>
        <source>Today</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="522"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="611"/>
        <source>Yesterday</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="526"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="529"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="612"/>
        <location filename="../common/widget/tab_widget/tabWidget.py" line="613"/>
        <source>No warnings</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WidgetAlertTab</name>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="73"/>
        <source>&lt;b&gt;1. Warning Methods:&lt;/b&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="76"/>
        <source>&lt;b&gt;2. Alert Channel Via:&lt;/b&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="208"/>
        <source>Voice</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="222"/>
        <source>Alarm Sound</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="240"/>
        <source>Notification Message</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="252"/>
        <source>Highlight Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="257"/>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="466"/>
        <source>Select your highlight type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="261"/>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="393"/>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="416"/>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="446"/>
        <source>Highlight until the user interacts with that camera.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="265"/>
        <source>Second</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="267"/>
        <source>Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="269"/>
        <source>Hour</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="270"/>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="421"/>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="453"/>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="472"/>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="480"/>
        <source>Highlight duration: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="298"/>
        <source>iVMS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="307"/>
        <source>iEMS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="316"/>
        <source>Tactical Operation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="328"/>
        <source>Internal SMS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="336"/>
        <source>Internal Email</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="347"/>
        <source>External SMS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="355"/>
        <source>External Email</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="366"/>
        <source>Telegram</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/widget_alert_tab.py" line="399"/>
        <source>Configure the highlight duration: </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WidgetConfigListUsersRole</name>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="52"/>
        <source>Number of users: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="53"/>
        <source>0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="62"/>
        <source>Search by name, email, phone number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="91"/>
        <source>No Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="107"/>
        <source>FULLNAME</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="107"/>
        <source>USER GROUP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="107"/>
        <source>EMAIL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="108"/>
        <source>PHONE NUMBER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="110"/>
        <source>ACTIONS</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WidgetConfigPermissionRole</name>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="43"/>
        <source>User group name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="43"/>
        <source>Enter Group Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="48"/>
        <source>Description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="48"/>
        <source>Enter Description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="53"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="54"/>
        <source>Active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="54"/>
        <source>In-active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="126"/>
        <source>System Permissions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="130"/>
        <source>Image Management</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="134"/>
        <source>Case Management Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="138"/>
        <source>Camera Management</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WidgetStatus</name>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="522"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="565"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="598"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="641"/>
        <source>In-Active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="524"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="575"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="600"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="651"/>
        <source>Active</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
